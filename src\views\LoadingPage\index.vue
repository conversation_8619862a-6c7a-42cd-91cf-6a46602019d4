<script setup lang="ts">
import { ref, onMounted } from "vue";

defineOptions({
  name: "LoadingPage",
});

interface Props {
  type?: "spinner" | "dots" | "pulse" | "wave" | "skeleton";
  text?: string;
  showProgress?: boolean;
  duration?: number;
}

const props = withDefaults(defineProps<Props>(), {
  type: "spinner",
  text: "Loading...",
  showProgress: false,
  duration: 3000,
});

const progress = ref(0);
const isComplete = ref(false);

onMounted(() => {
  if (props.showProgress) {
    const interval = setInterval(() => {
      progress.value += 1;
      if (progress.value >= 100) {
        clearInterval(interval);
        isComplete.value = true;
      }
    }, props.duration / 100);
  }
});
</script>

<template>
  <div class="loading-page">
    <div class="loading-content">
      <!-- Spinner Loading -->
      <div v-if="type === 'spinner'" class="spinner-container">
        <div class="spinner"></div>
      </div>

      <!-- Dots Loading -->
      <div v-else-if="type === 'dots'" class="dots-container">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>

      <!-- Pulse Loading -->
      <div v-else-if="type === 'pulse'" class="pulse-container">
        <div class="pulse-circle"></div>
      </div>

      <!-- Wave Loading -->
      <div v-else-if="type === 'wave'" class="wave-container">
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
      </div>

      <!-- Skeleton Loading -->
      <div v-else-if="type === 'skeleton'" class="skeleton-container">
        <div class="skeleton-line skeleton-title"></div>
        <div class="skeleton-line skeleton-text"></div>
        <div class="skeleton-line skeleton-text"></div>
        <div class="skeleton-line skeleton-short"></div>
      </div>

      <!-- Loading Text -->
      <div class="loading-text">{{ text }}</div>

      <!-- Progress Bar -->
      <div v-if="showProgress" class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
        <div class="progress-text">{{ progress }}%</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #ba1245 0%, #8b0e33 100%);
  z-index: 9999;
  font-family: "Inter", sans-serif;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-width: 200px;
}

// Spinner Loading
.spinner-container {
  width: 60px;
  height: 60px;
}

.spinner {
  width: 100%;
  height: 100%;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Dots Loading
.dots-container {
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}
.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// Pulse Loading
.pulse-container {
  width: 60px;
  height: 60px;
}

.pulse-circle {
  width: 100%;
  height: 100%;
  background: #667eea;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

// Wave Loading
.wave-container {
  display: flex;
  gap: 4px;
  align-items: end;
}

.wave-bar {
  width: 6px;
  height: 40px;
  background: #667eea;
  border-radius: 3px;
  animation: wave 1.2s ease-in-out infinite;
}

.wave-bar:nth-child(1) {
  animation-delay: 0s;
}
.wave-bar:nth-child(2) {
  animation-delay: 0.1s;
}
.wave-bar:nth-child(3) {
  animation-delay: 0.2s;
}
.wave-bar:nth-child(4) {
  animation-delay: 0.3s;
}
.wave-bar:nth-child(5) {
  animation-delay: 0.4s;
}

@keyframes wave {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

// Skeleton Loading
.skeleton-container {
  width: 200px;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 8px;
  margin-bottom: 12px;
  animation: skeleton 1.5s ease-in-out infinite;
}

.skeleton-title {
  height: 20px;
  width: 60%;
}

.skeleton-text {
  width: 100%;
}

.skeleton-short {
  width: 40%;
}

@keyframes skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Loading Text
.loading-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

// Progress Bar
.progress-container {
  width: 200px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

// Responsive
@media (max-width: 480px) {
  .loading-content {
    padding: 30px 20px;
    margin: 20px;
  }

  .loading-text {
    font-size: 16px;
  }
}
</style>
