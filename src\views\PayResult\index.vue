<script setup lang="ts" name="PayResult">
import { ref } from "vue";
import { getPayStatus } from "@/service";
import Loading from "@/components/Loading.vue";
import { useRoute } from "vue-router";
import successPng from "@/assets/img/success.png";
import unsuccessPng from "@/assets/img/unSuccessful.png";
import pendingPng from "@/assets/img/pending.png";

const PAGE_CONTENT = {
  0: {
    buttonText: "Back to Nustar",
    title: "Failed",
    img: unsuccessPng,
    titleColor: "#FF5353",
    urlParam: "type=pay&id=failed",
    extraContent: "You have successfully made a deposit.\n Good luck!",
  },
  1: {
    buttonText: "Go Play",
    title: "Success",
    img: successPng,
    imgStyle: { width: "266px", height: "auto" },
    titleColor: "#01D46A",
    urlParam: "type=pay&id=success",
    extraContent: "You have successfully made a deposit.\n Good luck!",
  },
  2: {
    buttonText: "Back to Nustar",
    title: "Pending",
    img: pendingPng,
    titleColor: "#5690FF",
    urlParam: "type=pay&id=pending",
    extraContent: "Your deposit request is currently being processed.\n Please kindly wait.",
  },
  3: {
    buttonText: "Back to Nustar",
    title: "Unsuccessful",
    img: unsuccessPng,
    titleColor: "#FF5353",
    urlParam: "type=pay&id=failed",
    extraContent:
      "Your deposit could not be completed due to a system error. Please try again later.",
  },
  4: {
    buttonText: "Back to Nustar",
    title: "Canceled",
    img: unsuccessPng,
    titleColor: "#FF5353",
    urlParam: "type=pay&id=canceled",
    extraContent:
      "Your deposit could not be completed due to a system error. Please try again later.",
  },
};

const route = useRoute();
const { query } = route;

const payStatus = ref<{
  status: number;
  content: string;
  urlParam: string;
  amount: string | number;
}>();

const loading = ref(false);

// 初始化支付结果页面
async function init() {
  loading.value = true;

  try {
    const requiredParams = {
      order_id: query.order_id as string,
      user_id: query.user_id as string,
      sign: query.sign as string,
      timestamp: query.timestamp as string,
      pay_channel: query.pay_channel as string,
    };

    // 验证必需参数
    const missingParams = Object.entries(requiredParams)
      .filter(([_, value]) => !value)
      .map(([key]) => key);

    if (missingParams.length > 0) {
      setErrorState("Missing required parameters");
      return;
    }

    const response = await getPayStatus(requiredParams);

    if (!response?.data?.status) {
      setErrorState("Invalid response data");
      return;
    }

    const statusConfig = PAGE_CONTENT[response.data.status];
    if (!statusConfig) {
      setErrorState("Unknown payment status");
      return;
    }

    payStatus.value = {
      ...response.data,
      ...statusConfig,
    };
  } catch (error) {
    console.error("Failed to get payment status:", error);
    setErrorState("Failed to retrieve payment status");
  } finally {
    loading.value = false;
  }
}

function setErrorState(message: string) {
  payStatus.value = {
    status: 0,
    content: message,
    amount: 0,
    ...PAGE_CONTENT[0],
  };
}
function closePage() {
  try {
    const isFirefoxOrChrome = /Firefox|Chrome/.test(navigator.userAgent);
    if (isFirefoxOrChrome) {
      window.location.href = "about:blank";
      window.close();
    } else {
      window.opener = null;
      window.open("", "_self");
      window.close();
    }
  } catch (e) {
    var newWindow = window.open("about:blank", "_self");
    if (newWindow) {
      newWindow.close();
    }
  }
}

function handleClick() {
  try {
    const timestamp = query.timestamp as string;
    const urlParams = `${payStatus.value?.urlParam}&amount=${payStatus.value?.amount}&timestamp=${timestamp}`;
    const { terminal } = query;
    // App  terminal 1: Google App ;2: Other App; 4: IOS App ; 8:GCash ;16:GCash Slots; 32:GCash Poker; 64:Maya; 128:Web
    if (["1", "2", "4"].includes(`${terminal}`)) {
      if (/iPhone|iPod|iPad/i.test(navigator.userAgent)) {
        location.href = "nustar://";
      } else if (/android/i.test(navigator.userAgent)) {
        const appUrl = import.meta.env.VITE_JUMP_APP_URL;
        location.href = `${appUrl}?${urlParams}`;
      }
      return;
    }

    // Web - close current tab
    closePage();
  } catch (error) {
    console.error("Error in handleClick:", error);
  }
}

init();
</script>

<template>
  <Loading v-if="loading" />
  <div v-else class="container">
    <div class="content">
      <img class="icon" :src="payStatus?.img" :style="payStatus?.imgStyle || {}" />
      <span :style="{ color: payStatus?.titleColor || '#5690FF' }">{{ payStatus?.title }}</span>
      <div class="bottom">
        {{ payStatus?.content || payStatus?.extraContent }}
      </div>
    </div>

    <div v-if="payStatus?.buttonText" class="button" @click="handleClick">
      {{ payStatus?.buttonText }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  height: 100vh;
  height: 100dvh;
  font-family: "Inter";

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 700;
    line-height: 48px;
    color: #ff5353;

    .icon {
      margin-bottom: 30px;
      width: 148px;
      height: auto;
    }
  }

  .bottom {
    white-space: pre-line;
    padding: 0 20px 60px;
    width: 100%;
    color: #8d949d;
    font-size: 14px;
    font-family: "Inter";
    font-weight: 400;
    line-height: 28px;
    letter-spacing: 0%;
    white-space: pre-line;
  }

  .button {
    width: 80%;
    margin: 30px auto;
    height: 48px;
    border-radius: 48px;
    line-height: 48px;
    font-size: 16px;
    font-weight: 800;
    color: #fff;
    background-color: #ac1140;
  }
}
</style>
