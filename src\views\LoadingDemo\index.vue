<script setup lang="ts">
import { ref } from 'vue'
import LoadingPage from '@/views/LoadingPage/index.vue'

defineOptions({
  name: 'LoadingDemo'
})

const showLoading = ref(false)
const loadingType = ref<'spinner' | 'dots' | 'pulse' | 'wave' | 'skeleton'>('spinner')
const loadingText = ref('Loading...')
const showProgress = ref(false)

const loadingTypes = [
  { value: 'spinner', label: '旋转加载' },
  { value: 'dots', label: '点点加载' },
  { value: 'pulse', label: '脉冲加载' },
  { value: 'wave', label: '波浪加载' },
  { value: 'skeleton', label: '骨架屏加载' }
]

const loadingTexts = [
  'Loading...',
  '正在加载中...',
  '请稍候...',
  '数据加载中...',
  '正在处理您的请求...'
]

function showLoadingDemo() {
  showLoading.value = true
  setTimeout(() => {
    showLoading.value = false
  }, 5000)
}

function toggleProgress() {
  showProgress.value = !showProgress.value
}
</script>

<template>
  <div class="loading-demo">
    <div class="demo-container">
      <h1 class="demo-title">Loading 页面演示</h1>
      
      <div class="demo-controls">
        <div class="control-group">
          <label class="control-label">加载类型:</label>
          <select v-model="loadingType" class="control-select">
            <option v-for="type in loadingTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </option>
          </select>
        </div>

        <div class="control-group">
          <label class="control-label">加载文本:</label>
          <select v-model="loadingText" class="control-select">
            <option v-for="text in loadingTexts" :key="text" :value="text">
              {{ text }}
            </option>
          </select>
        </div>

        <div class="control-group">
          <label class="control-checkbox">
            <input type="checkbox" v-model="showProgress" @change="toggleProgress">
            显示进度条
          </label>
        </div>

        <button @click="showLoadingDemo" class="demo-button">
          显示 Loading (5秒)
        </button>
      </div>

      <div class="demo-preview">
        <h3>当前配置预览:</h3>
        <div class="preview-info">
          <p><strong>类型:</strong> {{ loadingTypes.find(t => t.value === loadingType)?.label }}</p>
          <p><strong>文本:</strong> {{ loadingText }}</p>
          <p><strong>进度条:</strong> {{ showProgress ? '显示' : '隐藏' }}</p>
        </div>
      </div>
    </div>

    <!-- Loading Component -->
    <LoadingPage 
      v-if="showLoading"
      :type="loadingType"
      :text="loadingText"
      :show-progress="showProgress"
      :duration="5000"
    />
  </div>
</template>

<style lang="scss" scoped>
.loading-demo {
  min-height: 100vh;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Inter', sans-serif;
}

.demo-container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.demo-title {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
  font-size: 28px;
  font-weight: 600;
}

.demo-controls {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 40px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.control-select {
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #667eea;
  }
}

.control-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #555;
  cursor: pointer;

  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
  }
}

.demo-button {
  padding: 16px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.demo-preview {
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #667eea;

  h3 {
    margin-bottom: 16px;
    color: #333;
    font-size: 18px;
  }
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 8px;

  p {
    margin: 0;
    font-size: 14px;
    color: #666;

    strong {
      color: #333;
    }
  }
}

@media (max-width: 768px) {
  .demo-container {
    padding: 24px;
    margin: 0 10px;
  }

  .demo-title {
    font-size: 24px;
  }
}
</style>
