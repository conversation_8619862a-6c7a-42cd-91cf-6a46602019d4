<script setup lang="ts">
defineOptions({
  name: "LoadingPage",
});

interface Props {
  text?: string;
}

const props = withDefaults(defineProps<Props>(), {
  text: "Loading...",
});
</script>

<template>
  <div class="loading-page">
    <div class="loading-content">
      <img src="@/assets/img/Nustar.png" alt="" />
      <!-- Spinner Loading -->
      <div class="spinner-container">
        <div class="spinner"></div>
      </div>

      <!-- Loading Text -->
      <div class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #ba1245 0%, #8b0e33 100%);
  z-index: 9999;
  font-family: "Inter", sans-serif;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-width: 200px;

  img {
    width: 120px;
    height: auto;
  }
}

// Spinner Loading
.spinner-container {
  width: 60px;
  height: 60px;
}

.spinner {
  width: 100%;
  height: 100%;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ba1245;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Loading Text
.loading-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

// Responsive
@media (max-width: 480px) {
  .loading-content {
    padding: 30px 20px;
    margin: 20px;
  }

  .loading-text {
    font-size: 16px;
  }
}
</style>
